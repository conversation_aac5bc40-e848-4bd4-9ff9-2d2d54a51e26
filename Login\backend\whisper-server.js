// Whisper Speech-to-Text Server
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const OpenAI = require('openai');

const app = express();
const PORT = process.env.WHISPER_PORT || 4002;

// Initialize OpenAI client
let openai;
try {
  if (!process.env.OPENAI_API_KEY) {
    console.error('🔑 OpenAI API key not configured. Please set OPENAI_API_KEY in your .env file');
    console.log('📝 Copy .env.example to .env and add your OpenAI API key');
  } else {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    console.log('✅ OpenAI client initialized successfully');
  }
} catch (error) {
  console.error('❌ Error initializing OpenAI client:', error.message);
}

// Middleware
app.use(cors());
app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'audio-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB limit
  },
  fileFilter: function (req, file, cb) {
    // Check if file is audio
    const allowedMimes = [
      'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/m4a',
      'audio/mp4', 'audio/webm', 'audio/ogg', 'video/mp4',
      'video/mpeg', 'video/webm'
    ];

    if (allowedMimes.includes(file.mimetype) ||
        file.originalname.match(/\.(mp3|wav|m4a|mp4|mpeg|mpga|webm|ogg)$/i)) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed'), false);
    }
  }
});

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Signify.Ed Speech-to-Text API',
    status: 'running',
    openai_configured: !!openai,
    port: PORT
  });
});

// Whisper transcription endpoint
app.post('/api/whisper-transcribe', upload.single('audio'), async (req, res) => {
  console.log('📝 Transcription request received');

  try {
    // Check if OpenAI is configured
    if (!openai) {
      return res.status(500).json({
        error: 'OpenAI API not configured',
        details: 'Please set OPENAI_API_KEY in your .env file'
      });
    }

    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        error: 'No audio file provided',
        details: 'Please upload an audio file'
      });
    }

    const audioFile = req.file;
    console.log(`🎵 Processing audio file: ${audioFile.filename} (${audioFile.size} bytes)`);

    try {
      // Create a readable stream for the audio file
      const audioStream = fs.createReadStream(audioFile.path);
      audioStream.path = audioFile.filename; // OpenAI requires a filename

      // Call OpenAI Whisper API
      console.log('🤖 Calling OpenAI Whisper API...');
      const transcription = await openai.audio.transcriptions.create({
        file: audioStream,
        model: 'whisper-1',
        language: 'en', // You can make this configurable
        response_format: 'text'
      });

      console.log('✅ Transcription completed successfully');
      console.log(`📄 Result: ${transcription.substring(0, 100)}...`);

      // Clean up uploaded file
      fs.unlink(audioFile.path, (err) => {
        if (err) console.error('Error deleting temp file:', err);
      });

      res.json({
        text: transcription,
        success: true
      });

    } catch (apiError) {
      console.error('❌ OpenAI API Error:', apiError.message);

      // Clean up uploaded file
      fs.unlink(audioFile.path, (err) => {
        if (err) console.error('Error deleting temp file:', err);
      });

      // Handle specific OpenAI errors
      if (apiError.status === 401) {
        return res.status(500).json({
          error: 'Invalid OpenAI API key',
          details: 'Please check your OpenAI API key configuration'
        });
      } else if (apiError.status === 429) {
        return res.status(500).json({
          error: 'API quota exceeded',
          details: 'You have exceeded your OpenAI API quota. Please check your billing.'
        });
      } else {
        return res.status(500).json({
          error: 'Failed to transcribe audio',
          details: apiError.message
        });
      }
    }

  } catch (error) {
    console.error('❌ Server Error:', error.message);

    // Clean up uploaded file if it exists
    if (req.file) {
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('Error deleting temp file:', err);
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

// Proxy endpoints for training API (forward to Python FastAPI server)
const proxyEndpoints = [
  '/api/training-data',
  '/api/start-training',
  '/api/training-progress',
  '/api/models',
  '/api/custom-transcribe'
];

proxyEndpoints.forEach(endpoint => {
  // Handle GET requests
  app.get(endpoint, async (req, res) => {
    try {
      const response = await fetch(`http://localhost:8000${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        res.json(data);
      } else {
        res.status(response.status).json({ error: 'Training API not available' });
      }
    } catch (error) {
      res.status(503).json({ error: 'Training API not available', details: error.message });
    }
  });

  // Handle POST requests
  app.post(endpoint, upload.single('audio'), async (req, res) => {
    try {
      let body;
      let headers = {};

      if (req.file) {
        // For file uploads, create FormData
        const FormData = require('form-data');
        const formData = new FormData();
        formData.append('audio', fs.createReadStream(req.file.path), req.file.originalname);

        // Add other form fields
        Object.keys(req.body).forEach(key => {
          formData.append(key, req.body[key]);
        });

        body = formData;
        headers = formData.getHeaders();
      } else {
        // For JSON requests
        body = JSON.stringify(req.body);
        headers['Content-Type'] = 'application/json';
      }

      const response = await fetch(`http://localhost:8000${endpoint}`, {
        method: 'POST',
        headers: headers,
        body: body,
      });

      if (response.ok) {
        const data = await response.json();
        res.json(data);
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Training API error' }));
        res.status(response.status).json(errorData);
      }

      // Clean up uploaded file
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
          if (err) console.error('Error deleting temp file:', err);
        });
      }
    } catch (error) {
      res.status(503).json({ error: 'Training API not available', details: error.message });

      // Clean up uploaded file
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
          if (err) console.error('Error deleting temp file:', err);
        });
      }
    }
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'File too large',
        details: 'Audio file must be smaller than 25MB'
      });
    }
  }

  if (error.message === 'Only audio files are allowed') {
    return res.status(400).json({
      error: 'Invalid file type',
      details: 'Please upload an audio file (mp3, wav, m4a, etc.)'
    });
  }

  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    details: error.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Speech-to-Text server running on http://localhost:${PORT}`);
  console.log(`📊 OpenAI API configured: ${!!openai}`);
  console.log(`🔗 Training API proxy: http://localhost:8000`);

  if (!openai) {
    console.log('\n⚠️  To enable speech-to-text functionality:');
    console.log('1. Copy .env.example to .env');
    console.log('2. Add your OpenAI API key to the .env file');
    console.log('3. Restart the server');
  }
});

module.exports = app;
