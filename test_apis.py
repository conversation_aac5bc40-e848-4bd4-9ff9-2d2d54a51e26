#!/usr/bin/env python3
import requests
import json

def test_endpoint(url, name):
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print(f'✅ {name}: Working (Status: {response.status_code})')
            return True
        else:
            print(f'⚠️  {name}: Status {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ {name}: Failed - {str(e)}')
        return False

print('🧪 Testing API endpoints...')
print()

# Test AI Training API
test_endpoint('http://localhost:4001/api/models', 'AI Training API - Models')
test_endpoint('http://localhost:4001/api/training-data', 'AI Training API - Training Data')

# Test Whisper API
test_endpoint('http://localhost:4002/', 'Whisper API - Health Check')

print()
print('🌐 Frontend should be available at: http://localhost:5175')
