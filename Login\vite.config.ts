import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api/whisper-transcribe': {
        target: 'http://localhost:4002',
        changeOrigin: true,
        secure: false
      },
      // Training API endpoints (FastAPI server)
      '/api/training-data': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/api/start-training': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/api/training-progress': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/api/models': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/api/custom-transcribe': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      // All other API calls go to Node.js backend
      '/api': {
        target: 'http://localhost:4001',
        changeOrigin: true,
        secure: false
      },
    },
  },
})
